<template>
  <q-page>
    <draggable
      v-model="draggableBlocks"
      :component-data="{
        tag: 'div',
        type: 'transition-group',
        name: !blockCreatorStore.isDragging ? 'flip-list' : null,
      }"
      item-key="id"
      handle=".three-dot-menu"
      :animation="200"
      ghost-class="ghost"
      chosen-class="chosen"
      drag-class="drag"
      @start="onDragStart"
      @end="onDragEnd"
      @change="onDragChange"
    >
      <template #item="{ element: block, index }">
        <div
          :key="`${block.id}-${blockCreatorStore.totalSections}-${blockCreatorStore.forceUpdateTrigger}`"
          :ref="(el) => (blockCreatorUIStore.blockRefs[block.id] = el)"
          class="row justify-center draggable-item"
          :class="{ 'is-dragging': blockCreatorStore.isDragging }"
        >
          <div class="col-auto">
            <div
              v-if="blockCreatorStore.isSectionBlock(index) && blockCreatorStore.totalSections > 1"
              class="col-12 section-container"
              :key="`section-${blockCreatorStore.getSectionNumber(index)}-${blockCreatorStore.totalSections}-${blockCreatorStore.forceUpdateTrigger}-${block.id}`"
            >
              <div class="section-tab">
                ส่วนที่ {{ blockCreatorStore.getSectionNumber(index) }} จาก
                {{ blockCreatorStore.totalSections }}
              </div>
            </div>
            <div class="block-container">
              <div class="block-content full-width">
                <template v-if="block.type === 'HEADER'">
                  <HeaderBlock
                    :itemBlock="block"
                    :index="index"
                    :type="props.type"
                    class="evaluate-item"
                    :class="{
                      'no-top-left-radius':
                        blockCreatorStore.isSectionBlock(index) &&
                        blockCreatorStore.totalSections > 1,
                    }"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateHeaderBlock"
                    @delete="() => onClickDeleteBlock(block, index)"
                  />
                </template>

                <template v-else-if="block.type === 'IMAGE'">
                  <ImageBlock
                    :item-block="block"
                    class="evaluate-item"
                    @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                    @duplicate="handleDuplicateBlock"
                    @delete="() => onClickDeleteBlock(block, index)"
                    @update:image="handleImageUpdate"
                  />
                </template>

                <template v-else>
                  <ItemBlockProvider :block-id="block.id" :item-block="block">
                    <!-- <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="blockCreatorUIStore.handleQuestionUpdate"
                      @update:option="blockCreatorUIStore.handleOptionUpdate"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                    /> -->
                    <ItemBlockComponent
                      :item-block="block"
                      :type="props.type"
                      :question-number="getQuestionNumber(index)"
                      class="evaluate-item"
                      @focus-fab="blockCreatorUIStore.handleFocusFab(block.id)"
                      @duplicate="handleDuplicateBlock"
                      @delete="() => onClickDeleteBlock(block, index)"
                      @update:question="handleUpdateQuestion"
                      @update:option="blockCreatorUIStore.handleOptionUpdate"
                      @update:is-required="blockCreatorUIStore.handleIsRequiredUpdate"
                    />
                  </ItemBlockProvider>
                </template>
              </div>
            </div>
          </div>

          <div class="col-auto fixed-fab-col">
            <FloatActionBtnForBlock
              v-show="
                blockCreatorStore.selectedBlockId === `block-${block.id}` &&
                !blockCreatorStore.isDragging
              "
              :disabled="blockCreatorStore.isCreatingBlock"
              :type="props.type"
              @add="
                () => blockCreatorStore.handleAddBlockAfter(index, props.type, props.assessmentId)
              "
              @add-text="
                () => blockCreatorStore.handleAddHeaderAfter(index, props.type, props.assessmentId)
              "
              @add-section="handleAddSection"
              @add-image="(payload) => handleAddImageBlock(index, payload)"
              @image-uploaded="handleImageUploaded"
            />
          </div>
        </div>
      </template>
    </draggable>
  </q-page>
</template>

<script setup lang="ts">
import { watch, nextTick, onMounted, onUnmounted, computed, ref } from 'vue';
import { useBlockCreatorStore } from 'src/stores/block_creator';
import { useBlockCreatorUIStore } from 'src/stores/block_creator_ui';
import type { ItemBlock } from 'src/types/models';
import HeaderBlock from './HeaderBlock.vue';
import ItemBlockComponent from './ItemBlockComponent.vue';
import FloatActionBtnForBlock from './FloatActionBtnForBlock.vue';
import ItemBlockProvider from './ItemBlockProvider.vue';
import ImageBlock from './ImageBlock.vue';
import { defaultBlocks } from 'src/data/defaultBlocks';
import draggable from 'vuedraggable';
import { AssessmentService } from 'src/services/asm/assessmentService';
import { useGlobalStore } from 'src/stores/global';
import { duplicateBlockAtomic, getCurrentSection } from 'src/utils/block_helper';
import { ItemBlockService } from 'src/services/asm/itemBlockService';

const props = defineProps<{
  blocks: ItemBlock[];
  type: 'quiz' | 'evaluate';
  assessmentId?: number | null;
}>();

defineOptions({
  name: 'block-creator',
});

// Store instances
const blockCreatorStore = useBlockCreatorStore();
const blockCreatorUIStore = useBlockCreatorUIStore();
const globalStore = useGlobalStore();

// State variables
const isDragging = ref(false);
const blockCreationInProgress = ref(false);
const targetBlockId = ref<number | null>(null);
const fabPositionLock = ref(false);
const isCreatingBlock = ref(false);
let scrollTimeout: NodeJS.Timeout | null = null;

// Memoized assessment service - only recreate when type changes
const assessmentService = computed(() => new AssessmentService(props.type));

/**
 * Reusable function to refresh assessment data from backend
 * Centralizes the pattern of fetching and updating assessment data
 */
const refreshAssessmentData = async (focusBlockId?: number | null) => {
  try {
    // Preserve the current FAB position if needed
    const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
    const currentBlockId = currentSelectedBlockId
      ? Number(currentSelectedBlockId.split('-')[1])
      : null;

    // Use explicit block ID if provided, otherwise use current selection
    const blockIdToFocus = focusBlockId || currentBlockId;

    // Handle based on assessment type
    if (props.type === 'evaluate' && props.assessmentId) {
      // Fetch the latest assessment data with all blocks properly loaded
      const updatedAssessment = await assessmentService.value.fetchOne(props.assessmentId);

      if (updatedAssessment) {
        // Update the assessment in the store
        blockCreatorStore.currentAssessment = updatedAssessment;

        // Initialize blocks with the fresh assessment data
        if (updatedAssessment.itemBlocks) {
          blockCreatorStore.initializeBlocks(updatedAssessment.itemBlocks);
        }
      }
    } else if (props.type === 'quiz' && props.assessmentId) {
      // For quiz type, refresh via quiz store
      const quizStore = await import('src/stores/quiz');
      if (quizStore.useQuizStore) {
        const quiz = quizStore.useQuizStore();
        await quiz.fetchAssessmentById(props.assessmentId);
      }
    }

    // Restore focus to the specified block if it exists
    if (blockIdToFocus) {
      const blockStillExists = blockCreatorStore.blocks.some(
        (block) => block.id === blockIdToFocus,
      );

      if (blockStillExists) {
        blockCreatorStore.selectedBlockId = `block-${blockIdToFocus}`;

        // Scroll to the restored position after DOM updates
        await nextTick();
        scrollToTarget();
      }
    }

    return true;
  } catch (error) {
    console.error('An error occurred:', error);
    return false;
  }
};

// Draggable blocks computed property
const draggableBlocks = computed({
  get: () => blockCreatorStore.blocks.slice().sort((a, b) => a.sequence - b.sequence),
  set: (newBlocks: ItemBlock[]) => {
    blockCreatorStore.updateBlocksOrder(newBlocks);
  },
});

// Function to initialize blocks based on current data
const initializeBlocksFromData = async () => {
  let blocksToUse: ItemBlock[] = [];

  if (props.type === 'evaluate') {
    // For evaluate type, get blocks from the block creator store
    const assessmentBlocks = blockCreatorStore.currentAssessment?.itemBlocks;
    if (assessmentBlocks && assessmentBlocks.length > 0) {
      blocksToUse = assessmentBlocks;
    } else if (props.blocks && props.blocks.length > 0) {
      blocksToUse = props.blocks;
    } else {
      // Only use default blocks as last resort for evaluate type
      blocksToUse = defaultBlocks;
    }
  } else {
    // For quiz type, use props or default blocks
    blocksToUse = props.blocks && props.blocks.length > 0 ? props.blocks : defaultBlocks;
  }

  blockCreatorStore.initializeBlocks(blocksToUse);

  // Initialize with first block selected ONLY if FAB is not locked (e.g., during image upload)
  if (blockCreatorStore.blocks.length > 0 && !blockCreatorStore.fabPositionLock) {
    blockCreatorStore.selectedBlockId = `block-${blockCreatorStore.blocks[0]!.id}`;
    await nextTick();
    blockCreatorStore.scrollToTarget();
  }
};
type UpdateQuestionPayload = {
  action: 'refresh-block';
  itemBlockId: number;
};

const handleUpdateQuestion = async (payload: UpdateQuestionPayload) => {
  if (payload.action === 'refresh-block') {
    try {
      // Use the centralized refreshAssessmentData function to update data
      await refreshAssessmentData(payload.itemBlockId);
    } catch (error) {
      console.error('An error occurred:', error);
    }
  }
};

onMounted(async () => {
  await initializeBlocksFromData();
});

// Cleanup all timeouts and intervals on unmount
onUnmounted(() => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout);
    scrollTimeout = null;
  }
  // Clear block refs cache
  blockCreatorUIStore.blockRefsCache.clear();
});

// Watch for changes in the block creator store's current assessment
// This ensures that when data is fetched from backend, the blocks are re-initialized
watch(
  () => blockCreatorStore.currentAssessment,
  async (newAssessment) => {
    if (props.type === 'evaluate' && newAssessment?.itemBlocks) {
      await initializeBlocksFromData();
    }
  },
  { deep: true, immediate: false },
);

// Watch for changes in globalIsRequired to update all itemBlocks reactively
watch(
  () => blockCreatorStore.currentAssessment?.globalIsRequired,
  (newGlobalIsRequired) => {
    if (props.type === 'evaluate' && newGlobalIsRequired !== undefined) {
      blockCreatorStore.blocks.forEach((block, index) => {
        if (block.type !== 'HEADER' && block.type !== 'IMAGE') {
          const updatedBlock = {
            ...block,
            isRequired: Boolean(newGlobalIsRequired),
          };
          blockCreatorStore.updateBlock(updatedBlock, index);
        }
      });
    }
  },
  { immediate: false },
);

// TODO: Move to store - temporary placeholder
const handleAddSection = async () => {
  // Call the store method to handle section addition
  await blockCreatorStore.handleAddSection(props.type, props.assessmentId);
};

// Handle HeaderBlock duplication with backend persistence
const handleDuplicateHeaderBlock = async () => {
  // Get the currently selected block
  const selectedBlockIdValue = blockCreatorStore.selectedBlockId;
  if (!selectedBlockIdValue) {
    return;
  }

  // Extract block ID from selectedBlockId (format: "block-123")
  const blockId = Number(selectedBlockIdValue.split('-')[1]);
  if (!blockId || isNaN(blockId)) {
    return;
  }

  // Call the store method to handle header block duplication
  await blockCreatorStore.handleDuplicateHeaderBlock(blockId, props.type, props.assessmentId);
};

// Handle ItemBlock duplication with ATOMIC backend operation
// ATOMIC SOLUTION: Uses single backend API call to eliminate race condition completely
const handleDuplicateBlock = async () => {
  // 1. Get the currently selected block
  const selectedBlockIdValue = blockCreatorStore.selectedBlockId;
  if (!selectedBlockIdValue) {
    return;
  }

  // 2. Extract block ID from selectedBlockId (format: "block-123")
  const blockId = Number(selectedBlockIdValue.split('-')[1]);
  if (!blockId || isNaN(blockId)) {
    return;
  }

  // 3. Find the source block in the store
  const sourceBlockIndex = blockCreatorStore.blocks.findIndex((block) => block.id === blockId);
  if (sourceBlockIndex === -1 || !blockCreatorStore.blocks[sourceBlockIndex]) {
    return;
  }
  const sourceBlock = blockCreatorStore.blocks[sourceBlockIndex];

  // 4. Prevent duplication during other operations
  if (isCreatingBlock.value) {
    return;
  }

  try {
    // 5. Set up state for duplication process
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;
    globalStore.startSaveOperation('Duplicating block (atomic operation)...');

    // 6. Get assessment ID and current section
    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      return;
    }
    const currentSection = getCurrentSection(blockCreatorStore.blocks, sourceBlockIndex);

    // 7. ATOMIC DUPLICATION: Single backend call creates complete duplicate
    const duplicatedBlock = await duplicateBlockAtomic(
      blockId,
      {
        assessmentId,
        sequence: sourceBlock.sequence + 1, // Insert after source block
        section: currentSection,
      },
      assessmentService.value,
    );

    if (!duplicatedBlock) {
      globalStore.completeSaveOperation(false, 'Failed to duplicate block');
      return;
    }

    // 8. Update local state with the complete duplicated block
    const insertIndex = sourceBlockIndex + 1;
    // Use deep clone to avoid reference issues
    const clonedBlock = structuredClone
      ? structuredClone(duplicatedBlock)
      : JSON.parse(JSON.stringify(duplicatedBlock));
    blockCreatorStore.addBlock(clonedBlock, insertIndex);

    // 9. Update sequence numbers for all blocks
    blockCreatorStore.blocks.forEach((block, idx) => {
      if (block.sequence !== idx + 1) block.sequence = idx + 1;
    });

    // 10. Update store for evaluate type with deep cloning
    if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
      const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
      const clonedUpdatedBlock = structuredClone
        ? structuredClone(duplicatedBlock)
        : JSON.parse(JSON.stringify(duplicatedBlock));
      const newAssessmentBlocks = [
        ...currentBlocks.slice(0, insertIndex),
        clonedUpdatedBlock,
        ...currentBlocks.slice(insertIndex),
      ];

      newAssessmentBlocks.forEach((block, idx) => (block.sequence = idx + 1));
      blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
    }

    // 11. Set UI focus to duplicated block
    targetBlockId.value = duplicatedBlock.id;
    fabPositionLock.value = true;
    await setFabAndScroll(duplicatedBlock.id);
    blockCreatorStore.selectedBlockId = `block-${duplicatedBlock.id}`;

    // 12. Complete operation
    const hasContent =
      (duplicatedBlock.type === 'IMAGE' && duplicatedBlock.imageBody) ||
      (duplicatedBlock.options && duplicatedBlock.options.length > 0) ||
      (duplicatedBlock.questions && duplicatedBlock.questions.length > 0) ||
      (duplicatedBlock.headerBody &&
        (duplicatedBlock.headerBody.title || duplicatedBlock.headerBody.description));

    globalStore.completeSaveOperation(
      true,
      hasContent
        ? 'Block duplicated successfully with complete content (atomic)'
        : 'Block duplicated successfully (atomic operation)',
    );

    // 13. Refresh the entire assessment to ensure consistency
    try {
      await refreshAssessmentData(duplicatedBlock.id);
    } catch (refreshError) {
      console.error('Refresh error occurred:', refreshError);
    }

    // 14. Clean up state after DOM is stable
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      blockCreatorStore.selectedBlockId = `block-${duplicatedBlock.id}`;
    }, 300); // Reduced timeout since no race condition
  } catch (error) {
    console.error('❌ Atomic duplication error:', error);
    globalStore.completeSaveOperation(false, 'Error duplicating block');
  } finally {
    isCreatingBlock.value = false;
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

// Helper function for scrolling to target
const scrollToTarget = () => {
  blockCreatorStore.scrollToTarget();
};

// Helper function for setting FAB position and scrolling
const setFabAndScroll = async (blockId: number) => {
  blockCreatorStore.setFabPosition(blockId, false);
  await nextTick();
  scrollToTarget();
};

const onClickDeleteBlock = async (item: ItemBlock, index: number) => {
  // Enhanced pre-deletion validation
  if (!item.id) {
    return;
  }

  if (!item.assessmentId) {
    return;
  }

  // Determine appropriate deletion message based on block type
  const getDeleteMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Deleting header...';
      case 'IMAGE':
        return 'Deleting image...';
      default:
        return 'Deleting question...';
    }
  };

  // Determine appropriate success message based on block type
  const getSuccessMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Header deleted successfully';
      case 'IMAGE':
        return 'Image deleted successfully';
      default:
        return 'Question deleted successfully';
    }
  };

  // Determine appropriate error message based on block type
  const getErrorMessage = (blockType: string): string => {
    switch (blockType) {
      case 'HEADER':
        return 'Failed to delete header';
      case 'IMAGE':
        return 'Failed to delete image';
      default:
        return 'Failed to delete question';
    }
  };

  try {
    // Enhanced validation for header blocks
    if (item.type === 'HEADER' && !item.headerBody) {
      return;
    }

    // Enhanced validation using block creator store
    if (props.type === 'evaluate') {
      const deletionValidation = blockCreatorStore.validateBlockDeletion(item.id);

      if (!deletionValidation.canDelete) {
        return;
      }
    }

    // Start save operation indicator with appropriate message
    globalStore.startSaveOperation(getDeleteMessage(item.type));

    // Delete the ItemBlock (this will cascade delete related entities)
    const deletedBlock = await assessmentService.value.deleteBlock(item);

    if (deletedBlock !== undefined) {
      // Complete save operation successfully
      globalStore.completeSaveOperation(true, getSuccessMessage(item.type));

      // Perform UI cleanup
      await handleBlockDeletionCleanup(item, index);
    } else {
      // Handle case where deletion didn't return expected result
      globalStore.completeSaveOperation(false, getErrorMessage(item.type));
    }
  } catch {
    // Complete save operation with error
    globalStore.completeSaveOperation(false, getErrorMessage(item.type));
  }
};

// Separate function to handle UI cleanup after successful deletion
const handleBlockDeletionCleanup = async (item: ItemBlock, index: number) => {
  try {
    // เตรียม targetIndex สำหรับการ focus หลังจากลบ
    const targetIndex = index > 0 ? index - 1 : 0;
    let focusBlockId = null;

    // กำหนดตำแหน่ง block ที่จะ focus หลังการลบ
    if (blockCreatorStore.blocks.length > 0) {
      const safeTargetIndex = Math.min(targetIndex, blockCreatorStore.blocks.length - 1);
      const targetBlock = blockCreatorStore.blocks[safeTargetIndex];

      if (targetBlock) {
        focusBlockId = targetBlock.id;
      }
    }

    // ใช้ฟังก์ชัน refreshAssessmentData เพื่ออัปเดตข้อมูลจาก backend
    if (
      (props.type === 'evaluate' && blockCreatorStore.currentAssessment?.id) ||
      (props.type === 'quiz' && props.assessmentId)
    ) {
      // รีเฟรชข้อมูล โดยกำหนด block ที่ต้องการ focus
      await refreshAssessmentData(focusBlockId);
    } else {
      // สำหรับกรณีที่ไม่มี assessment (เช่น test mode)
      await blockCreatorStore.deleteBlock(index);

      // จัดการ focus ในกรณีที่ไม่มีการ refresh data
      if (focusBlockId && blockCreatorStore.blocks.length > 0) {
        blockCreatorStore.selectedBlockId = `block-${focusBlockId}`;
        await setFabAndScroll(focusBlockId);
      } else {
        blockCreatorStore.selectedBlockId = undefined;
      }
    }
  } catch {
    // Error occurred during block deletion cleanup
  }
};

const handleAddImageBlock = async (
  index: number,
  payload: { callback: (id: number | null) => void },
) => {
  // Prevent multiple simultaneous block creation
  if (isCreatingBlock.value) {
    payload.callback(null);
    return;
  }

  try {
    isCreatingBlock.value = true;
    blockCreationInProgress.value = true;

    // Start save operation indicator
    globalStore.startSaveOperation('Creating image block...');

    const assessmentId = props.assessmentId || blockCreatorStore.getAssessmentId();
    if (!assessmentId) {
      globalStore.completeSaveOperation(false, 'Assessment ID not found');
      payload.callback(null);
      return;
    }

    const currentSection = getCurrentSection(blockCreatorStore.blocks, index);

    const newImageData = {
      assessmentId: assessmentId,
      sequence: index + 2,
      section: currentSection,
      type: 'IMAGE' as const,
      isRequired: false,
    };

    // Call backend API to create the image block (without image data)
    const addedBlock = await assessmentService.value.createBlock(newImageData);

    if (addedBlock) {
      // CRITICAL: Set target block ID to allow only this block to receive focus
      targetBlockId.value = addedBlock.id;

      // CRITICAL: Lock FAB position BEFORE any DOM updates to prevent jumping
      fabPositionLock.value = true;
      blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;

      // Add to local store with backend response data
      blockCreatorStore.addBlock(addedBlock, index);

      // Update the evaluate form store if this is an evaluate type
      if (props.type === 'evaluate' && blockCreatorStore.currentAssessment) {
        const currentBlocks = blockCreatorStore.currentAssessment.itemBlocks || [];
        // Insert at correct position (index + 1)
        const newAssessmentBlocks = [
          ...currentBlocks.slice(0, index + 1),
          addedBlock,
          ...currentBlocks.slice(index + 1),
        ];
        // Update sequences for assessment blocks
        newAssessmentBlocks.forEach((block, idx) => {
          block.sequence = idx + 1;
        });
        blockCreatorStore.currentAssessment.itemBlocks = newAssessmentBlocks;
      }

      // Complete save operation successfully
      globalStore.completeSaveOperation(true, 'Image block created successfully');

      // Set FAB position and scroll (FAB is already locked to correct position)
      await setFabAndScroll(addedBlock.id);

      // Release locks after DOM is stable
      setTimeout(() => {
        fabPositionLock.value = false;
        blockCreationInProgress.value = false;
        targetBlockId.value = null;
        // Final confirmation of FAB position
        blockCreatorStore.selectedBlockId = `block-${addedBlock.id}`;
      }, 800);

      // Return the created block ID to the callback
      payload.callback(addedBlock.id);
    } else {
      globalStore.completeSaveOperation(false, 'Failed to create image block');
      payload.callback(null);
    }
  } catch {
    globalStore.completeSaveOperation(false, 'Error creating image block');
    payload.callback(null);
  } finally {
    isCreatingBlock.value = false;
    // Clean up state in case of errors
    if (blockCreationInProgress.value) {
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
      fabPositionLock.value = false;
    }
  }
};

const handleImageUploaded = async () => {
  // This function is called when the image upload is completed
  // We need to refresh the current assessment data to get the updated imageBody

  try {
    // CRITICAL: Preserve the current FAB position before refreshing data
    const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
    const currentBlockId = currentSelectedBlockId
      ? Number(currentSelectedBlockId.split('-')[1])
      : null;

    // CRITICAL: Lock FAB position to prevent unwanted changes during refresh
    fabPositionLock.value = true;
    blockCreationInProgress.value = true;
    if (currentBlockId) {
      targetBlockId.value = currentBlockId;
    }

    // Use our centralized refresh function
    await refreshAssessmentData(currentBlockId);

    // CRITICAL: Restore FAB position to the ImageBlock that was being worked on
    if (currentSelectedBlockId && currentBlockId) {
      // Verify the block still exists
      const blockStillExists = blockCreatorStore.blocks.some(
        (block) => block.id === currentBlockId,
      );

      if (blockStillExists) {
        blockCreatorStore.selectedBlockId = currentSelectedBlockId;

        // Scroll to the restored position
        await nextTick();
        await nextTick();
        scrollToTarget();
      }
    }

    // Release FAB locks after refresh is complete
    setTimeout(() => {
      fabPositionLock.value = false;
      blockCreationInProgress.value = false;
      targetBlockId.value = null;
    }, 300);
  } catch (error) {
    console.error('An error occurred while refreshing assessment data:', error);
    globalStore.completeSaveOperation(false, 'Failed to refresh image data');
  }
};
// Handle image dimension updates from ImageBlock
const handleImageUpdate = async (updateData: {
  itemBlockId: number;
  dimensions: { width: number; height: number };
}) => {
  try {
    // CRITICAL: Preserve the current FAB position before refreshing data
    const currentSelectedBlockId = blockCreatorStore.selectedBlockId;
    const currentBlockId = currentSelectedBlockId
      ? Number(currentSelectedBlockId.split('-')[1])
      : null;

    // Update dimensions in backend via itemBlockService API
    const itemBlockService = new ItemBlockService();

    // Call API to update the dimensions
    await itemBlockService.updateDimensions(updateData.itemBlockId, updateData.dimensions);

    // Use the centralized refresh function
    if (props.type === 'evaluate' || props.type === 'quiz') {
      // For evaluate or quiz, refresh the entire assessment
      await refreshAssessmentData(updateData.itemBlockId);
    } else {
      // For other cases (e.g., test mode), just refresh the specific block
      const blockIndex = blockCreatorStore.blocks.findIndex(
        (block) => block.id === updateData.itemBlockId,
      );

      if (blockIndex !== -1) {
        const latestBlock = await itemBlockService.getOne(updateData.itemBlockId);
        if (latestBlock) {
          blockCreatorStore.updateBlock(latestBlock, blockIndex);
        }
      }
    }

    // Restore the current block selection if needed
    if (currentSelectedBlockId && currentBlockId === updateData.itemBlockId) {
      blockCreatorStore.selectedBlockId = currentSelectedBlockId;
    }
  } catch (error) {
    console.error('An error occurred:', error);
  }
};

// Drag and drop event handlers
const onDragStart = () => {
  isDragging.value = true;
};

const onDragEnd = () => {
  isDragging.value = false;
};

const onDragChange = async () => {
  // Handle drag change events and sync with backend

  try {
    // Only sync when we have blocks
    if (blockCreatorStore.blocks.length > 0) {
      // Start save operation indicator
      globalStore.startSaveOperation('Updating order...');

      // Get current blocks with updated sequences, filtering out invalid blocks
      const validBlocks = blockCreatorStore.blocks.filter((block) => {
        const isValid = block.id && !isNaN(Number(block.id)) && Number(block.id) > 0;
        if (!isValid) {
          // Add logic to handle invalid blocks if necessary
        }
        return isValid;
      });

      if (validBlocks.length === 0) {
        globalStore.completeSaveOperation(false, 'No valid blocks to update');
        return;
      }

      const blocksToUpdate = validBlocks.map((block, index) => ({
        ...block,
        sequence: index + 1, // Ensure sequence matches current order
      }));

      // Call backend API to update sequences
      const result = await assessmentService.value.updateBlockSequences(blocksToUpdate);

      if (result?.success) {
        // Refresh the assessment data after updating order using our centralized function
        await refreshAssessmentData();

        // Complete save operation successfully
        globalStore.completeSaveOperation(true, 'Order updated successfully');
      } else {
        // Handle case where API call didn't return expected result
        globalStore.completeSaveOperation(false, 'Failed to update order');
      }
    }
  } catch (error) {
    console.error('An error occurred while updating order:', error);
    globalStore.completeSaveOperation(false, 'Error updating order');
  }
};
// Optimized watcher with debouncing for better performance
watch(
  () => blockCreatorStore.selectedBlockId,
  (newValue) => {
    // During block creation, aggressively enforce the target position
    if (blockCreationInProgress.value && targetBlockId.value) {
      const expectedId = `block-${targetBlockId.value}`;
      if (newValue !== expectedId) {
        // Use nextTick to avoid infinite loops
        void nextTick(() => {
          blockCreatorStore.selectedBlockId = expectedId;
        });
        return;
      }
    }

    // Debounced scroll behavior for better performance
    if (scrollTimeout) {
      clearTimeout(scrollTimeout);
    }
    scrollTimeout = setTimeout(() => {
      scrollToTarget();
    }, 16);
  },
  { flush: 'post' }, // Run after DOM updates for better performance
);

const getQuestionNumber = (index: number): number => {
  return (
    blockCreatorStore.blocks
      .slice(0, index)
      .filter((block) => block.type !== 'HEADER' && block.type !== 'IMAGE').length + 1
  );
};
</script>

<style scoped>
.fixed-fab-col {
  width: 48px;
}

.section-container {
  position: relative;
  z-index: 1;
  margin-bottom: 0;
}

.section-tab {
  background-color: #673ab7;
  color: white;
  font-weight: 500;
  padding: 6px 16px;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
  font-size: 14px;
  width: fit-content;
  position: relative;
  left: 0;
}

.no-top-left-radius {
  border-top-left-radius: 0 !important;
}

/* Drag and Drop Styles */
.draggable-item {
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.draggable-item.is-dragging {
  opacity: 0.8;
}

.block-container {
  position: relative;
  width: 100%;
}

.block-content {
  flex: 1;
}

/* Drag states */
.ghost {
  opacity: 0.5;
  background: #f0f0f0;
  border: 2px dashed #ccc;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* Flip animation for smooth transitions */
.flip-list-move,
.flip-list-enter-active,
.flip-list-leave-active {
  transition: all 0.3s ease;
}

.flip-list-enter-from,
.flip-list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.flip-list-leave-active {
  position: absolute;
}
</style>
