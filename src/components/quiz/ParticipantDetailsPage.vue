<!-- eslint-disable @typescript-eslint/no-explicit-any -->
<template>
  <q-page class="bg-page-primary q-pa-md">
    <div class="container">
      <!-- Header -->
      <q-card class="q-mb-lg">
        <q-card-section class="row items-center">
          <q-btn
            flat
            round
            dense
            icon="arrow_back"
            @click="goBack"
            color="accent"
            size="md"
            class="q-mr-md"
          >
            <q-tooltip>กลับไปหน้าก่อน</q-tooltip>
          </q-btn>
          <div>
            <div class="header text-weight-bold text-accent">
              <q-icon name="assignment" color="accent" class="q-mr-sm" /> รายละเอียดผู้เข้าสอบ
            </div>
            <div class="sub-body text-grey-7">ข้อมูลและผลการทดสอบ</div>
          </div>
        </q-card-section>
      </q-card>

      <!-- Loading State -->
      <q-card v-if="dashboardStore.isLoadingParticipantDetails" class="q-mb-lg">
        <q-card-section class="text-center q-py-xl">
          <q-spinner-dots size="3em" color="accent" />
          <div class="body q-mt-md text-grey-8">กำลังโหลดข้อมูล...</div>
        </q-card-section>
      </q-card>

      <!-- Error State -->
      <q-banner
        v-if="dashboardStore.error && !dashboardStore.isLoadingParticipantDetails"
        class="bg-negative text-white q-mb-lg"
        rounded
      >
        <template v-slot:avatar>
          <q-icon name="error" />
        </template>
        <div>
          <div class="text-weight-bold">เกิดข้อผิดพลาด!</div>
          <div>{{ dashboardStore.error }}</div>
        </div>
        <template v-slot:action>
          <q-btn flat color="white" label="ลองใหม่" @click="retryFetch">
            <q-icon name="refresh" class="q-ml-sm" />
          </q-btn>
          <q-btn flat color="white" label="ล้าง" @click="dashboardStore.clearError">
            <q-icon name="clear" class="q-ml-sm" />
          </q-btn>
        </template>
      </q-banner>

      <!-- Participant Details Content -->
      <div v-if="dashboardStore.participantDetails && !dashboardStore.isLoadingParticipantDetails">
        <!-- Participant Summary Card -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <div class="header text-weight-bold text-accent q-mb-md">
              <q-icon name="person" color="accent" class="q-mr-sm" />
              ข้อมูลสรุป
            </div>

            <div class="row q-col-gutter-md">
              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="badge" color="accent" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">ชื่อผู้เข้าสอบ</q-item-label>
                    <q-item-label class="body text-weight-bold text-grey-9">
                      {{ participantData?.userName }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="grade" color="warning" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">คะแนนรวม</q-item-label>
                    <q-item-label class="body text-weight-bold text-grey-9">
                      {{ participantData?.totalScore }}/{{ participantData?.maxScore }}
                      <span class="text-grey-6">
                        ({{ Number(participantData?.scorePercentage).toFixed(2) }}%)
                      </span>
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="schedule" color="info" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">เวลาเริ่ม</q-item-label>
                    <q-item-label class="body text-grey-9">
                      {{ formatDateTime(participantData?.startTime) }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>

              <div class="col-12 col-md-6">
                <q-item>
                  <q-item-section avatar>
                    <q-icon name="done_all" color="positive" />
                  </q-item-section>
                  <q-item-section>
                    <q-item-label class="sub-body text-grey-7">เวลาสิ้นสุด</q-item-label>
                    <q-item-label class="body text-grey-9">
                      {{ formatDateTime(participantData?.endTime) }}
                    </q-item-label>
                  </q-item-section>
                </q-item>
              </div>
            </div>
          </q-card-section>
        </q-card>

        <!-- Questions and Answers -->
        <q-card class="q-mb-lg">
          <q-card-section>
            <div class="header text-weight-bold text-accent q-mb-md q-pb-md">
              <q-icon name="description" color="accent" class="q-mr-sm" />
              รายละเอียดคำตอบ
            </div>

            <div class="q-gutter-md">
              <q-card
                v-for="question in paginatedQuestions"
                :key="question.questionId"
                bordered
                flat
              >
                <q-card-section>
                  <div class="row justify-between items-start q-mb-md">
                    <div class="row items-center">
                      <q-chip
                        color="primary"
                        text-color="dark"
                        :label="question.questionSequence"
                        square
                      />
                      <div class="body text-weight-medium text-grey-9 q-ml-md">
                        {{ question.questionText }}
                      </div>
                    </div>
                    <div class="row q-gutter-sm">
                      <q-chip
                        color="secondary"
                        text-color="white"
                        :label="`${question.score} คะแนน`"
                        icon="star"
                        square
                      />
                      <!-- Only show correct/incorrect chip for non-TEXTFIELD questions -->
                      <q-chip
                        v-if="question.questionType !== 'TEXTFIELD'"
                        :color="question.isCorrect ? 'positive' : 'negative'"
                        text-color="white"
                        :label="question.isCorrect ? 'ถูกต้อง' : 'ผิด'"
                        :icon="question.isCorrect ? 'check_circle' : 'cancel'"
                        square
                      />
                      <!-- For TEXTFIELD questions, show answered status -->
                      <q-chip
                        v-else-if="question.questionType === 'TEXTFIELD'"
                        color="info"
                        text-color="white"
                        :label="question.textAnswer ? 'ตอบแล้ว' : 'ไม่ได้ตอบ'"
                        :icon="question.textAnswer ? 'edit' : 'edit_off'"
                        square
                      />
                    </div>
                  </div>

                  <div v-if="question.selectedOptionText || question.textAnswer" class="q-mb-md">
                    <q-separator class="q-mb-md" />
                  </div>

                  <!-- Display text answer for TEXTFIELD questions -->
                  <div
                    v-if="question.questionType === 'TEXTFIELD' && question.textAnswer"
                    class="q-mb-md"
                  >
                    <div class="body text-weight-medium text-grey-8 q-mb-sm">
                      <q-icon name="edit" color="grey-7" class="q-mr-xs" />
                      คำตอบที่กรอก:
                    </div>
                    <q-card flat bordered class="q-pa-md bg-grey-1">
                      <div class="body text-grey-9">
                        {{ question.textAnswer }}
                      </div>
                    </q-card>
                  </div>

                  <!-- Display options for multiple choice questions -->
                  <div
                    v-if="
                      question.questionType !== 'TEXTFIELD' &&
                      question.options &&
                      question.options.length > 0
                    "
                  >
                    <div class="body text-weight-medium text-grey-8 q-mb-md">
                      <q-icon name="list" color="grey-7" class="q-mr-xs" />
                      ตัวเลือกทั้งหมด:
                    </div>
                    <div class="q-gutter-sm">
                      <q-item
                        v-for="option in question.options"
                        :key="option.id"
                        class="q-mb-md rounded-borders"
                        :class="getOptionBorderClass(option)"
                        dense
                      >
                        <q-item-section avatar>
                          <div class="flex items-center">
                            <!-- Icon for checkbox questions -->
                            <q-icon
                              v-if="question.questionType === 'CHECKBOX'"
                              :name="option.isSelected ? 'check_box' : 'check_box_outline_blank'"
                              :color="getCheckboxIconColor(option)"
                              size="md"
                            />
                            <!-- Icon for radio questions -->
                            <q-icon
                              v-else-if="option.isSelected && option.value === 1"
                              name="check_circle"
                              color="positive"
                              size="md"
                            />
                            <q-icon
                              v-else-if="!option.isSelected && option.value === 1"
                              name="check_circle_outline"
                              color="positive"
                              size="md"
                            />
                            <q-icon
                              v-else-if="option.isSelected && option.value === 0"
                              name="cancel"
                              color="negative"
                              size="md"
                            />
                            <q-icon v-else name="radio_button_unchecked" color="grey-6" size="md" />
                          </div>
                        </q-item-section>

                        <q-item-section>
                          <div class="row items-center q-gutter-sm">
                            <q-item-label :class="getOptionTextClass(option)" class="body">
                              {{ option.text }}
                            </q-item-label>
                            <q-chip
                              v-if="option.isSelected"
                              color="dark"
                              text-color="white"
                              size="sm"
                              dense
                              label="ตัวเลือกที่เลือก"
                              icon="touch_app"
                            />
                          </div>
                        </q-item-section>
                      </q-item>
                    </div>
                  </div>
                </q-card-section>
              </q-card>

              <!-- Pagination Component -->
              <div class="row justify-center q-mt-lg">
                <div class="col-12 text-center q-mb-md">
                  <div class="body text-grey-7">
                    แสดงคำถามที่ {{ (currentPage - 1) * itemsPerPage + 1 }}-{{
                      Math.min(
                        currentPage * itemsPerPage,
                        dashboardStore.participantDetails?.total || 0,
                      )
                    }}
                    จากทั้งหมด {{ dashboardStore.participantDetails?.total || 0 }} คำถาม
                  </div>
                </div>
                <q-pagination
                  v-model="currentPage"
                  :max="totalPages"
                  :max-pages="6"
                  :boundary-links="true"
                  :direction-links="true"
                  color="black"
                  @update:model-value="handlePageChange"
                />
              </div>
            </div>
          </q-card-section>
        </q-card>
      </div>

      <!-- No Data State -->
      <q-card
        v-else-if="!dashboardStore.isLoadingParticipantDetails && !dashboardStore.error"
        class="q-mb-lg"
      >
        <q-card-section class="text-center q-py-xl">
          <q-icon name="inbox" size="4em" color="grey-5" />
          <div class="header text-grey-8 q-mt-md">ไม่พบข้อมูล</div>
          <div class="sub-body text-grey-6">ไม่พบข้อมูลผู้เข้าสอบที่คุณต้องการ</div>
        </q-card-section>
      </q-card>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useQuizDashboardStore } from 'src/stores/quizdashboardStore';
import type { DataParams } from 'src/types/data';

const route = useRoute();
const router = useRouter();
const dashboardStore = useQuizDashboardStore();

// Get participant data from the first item in the DataResponse
const participantData = computed(() => {
  return dashboardStore.participantDetails?.data?.[0] || null;
});

// Initialize pagination parameters with type safety
const paginationParams = ref<DataParams>({
  page: 1,
  limit: 5,
  sortBy: null,
  order: 'ASC',
  search: null,
});

// Update currentPage and itemsPerPage to use paginationParams
const currentPage = computed({
  get: () => paginationParams.value.page,
  set: (value) => {
    paginationParams.value.page = value;
  },
});

const itemsPerPage = computed({
  get: () => paginationParams.value.limit,
  set: (value) => {
    paginationParams.value.limit = value;
  },
});

const participantId = Number(route.params.participantId);

// Pagination state with type safety
// const currentPage = ref<number>(1);
// const itemsPerPage = ref<number>(5);

interface Option {
  id: number;
  text: string;
  isSelected: boolean;
  value: number;
}

interface Question {
  questionId: number;
  questionSequence: number;
  questionText: string;
  questionType?: string;
  score: number;
  isCorrect: boolean;
  selectedOptionText?: string;
  textAnswer?: string;
  options: Array<Option>;
}

// Compute total pages based on backend pagination metadata
const totalPages = computed((): number => {
  if (!dashboardStore.participantDetails) return 1;
  return Math.ceil(dashboardStore.participantDetails.total / itemsPerPage.value);
});

// Get questions directly from participantData (already paginated by backend)
const paginatedQuestions = computed((): Question[] => {
  return participantData.value?.questions || [];
});

// Methods
function formatDateTime(dateString: string | undefined): string {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleString('th-TH', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  } catch (e) {
    console.error('[ParticipantDetailsPage] Error formatting date:', e);
    return dateString;
  }
}

function goBack() {
  router.back();
}

async function retryFetch() {
  dashboardStore.clearError();
  await fetchParticipantDetails();
}

async function fetchParticipantDetails() {
  if (participantId && !isNaN(participantId)) {
    try {
      await dashboardStore.fetchParticipantDetails(participantId, paginationParams.value);
    } catch (error) {
      console.error('Failed to fetch participant details:', error);
    }
  }
}

async function handlePageChange(newPage: number) {
  paginationParams.value.page = newPage;
  window.scrollTo({ top: 0, behavior: 'smooth' });
  await fetchParticipantDetails();
}

// UI helper functions for option styling
function getOptionBorderClass(option: Option): string {
  if (option.isSelected && option.value === 1) {
    // คำตอบที่เลือกและถูกต้อง
    return 'border-positive';
  } else if (option.isSelected && option.value === 0) {
    // คำตอบที่เลือกแต่ผิด
    return 'border-negative';
  } else if (!option.isSelected && option.value === 1) {
    // คำตอบที่ถูกต้องแต่ไม่ได้เลือก
    return 'border-positive';
  }
  return 'border-grey-4';
}

function getCheckboxIconColor(option: Option): string {
  if (option.value === 1) {
    return 'positive';
  } else if (option.isSelected && option.value === 0) {
    return 'negative';
  }
  return 'grey-6';
}

function getOptionTextClass(option: Option): string {
  if (option.value === 1) {
    // คำตอบที่ถูกต้อง (ไม่ว่าจะเลือกหรือไม่)
    return 'text-weight-bold text-positive';
  } else if (option.isSelected && option.value === 0) {
    // คำตอบที่เลือกแต่ผิด
    return 'text-weight-medium text-negative';
  }
  return 'text-grey-8';
}

// Lifecycle
onMounted(async () => {
  console.log('[ParticipantDetailsPage] Component mounted. Participant ID:', participantId);
  // Scroll to top when component mounts
  window.scrollTo({ top: 0, behavior: 'smooth' });
  await fetchParticipantDetails();
});
</script>

<style scoped>
/* Clean minimal styles using Quasar framework and app.scss classes */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* Use app.scss classes for consistency */
.bg-page-primary {
  min-height: 100vh;
}

/* Border classes for option styling */
.border-positive {
  border: 2px solid var(--q-positive) !important;
}

.border-negative {
  border: 2px solid var(--q-negative) !important;
}

.border-grey-4 {
  border: 1px solid var(--q-grey-4) !important;
}

/* Simple responsive adjustments */
@media (max-width: 768px) {
  .container {
    padding: 15px;
  }

  .q-card {
    margin-bottom: 20px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 10px;
  }
}
</style>
