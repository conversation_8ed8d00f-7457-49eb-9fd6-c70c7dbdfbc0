import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import authService from '../services/authService';
import { jwtDecode, type JwtPayload } from 'jwt-decode';
import { type User, type Permission } from 'src/types/models';
import { useUtilsStore } from './utils';
import type { AxiosError } from 'axios';

export const useAuthStore = defineStore('auth', () => {
  // State
  const loginUsername = ref('');
  const loginPassword = ref('');
  const incorrectUsernamePasswordStatus = ref(false);
  const notifyDialog = ref(false);
  const notifyMessage = ref('');
  const utilsStore = useUtilsStore();
  const secretKey = 'E13qDGn!Z|"38y/BUvFjl$cA-vs4)P';
  const currentRoleName = ref();

  // Set the current role name based on the first role of the user
  const setCurrentRole = (roleName: string) => {
    const user = getCurrentUser();
    if (user && user.roles) {
      const role = user.roles.find((r) => r.name === roleName);
      if (role) {
        user.roles = [role];
        currentRoleName.value = roleName;
        localStorage.setItem('currentRole', roleName);
      }
    }
  };

  // Initialize currentRoleName when the store is created
  currentRoleName.value =
    localStorage.getItem('currentRole') || getCurrentUser()?.roles?.[0]?.name || 'Guest';

  // Getters
  const isLoggedIn = computed(() => {
    return Boolean(localStorage.getItem('access_token'));
  });

  // Flatten all permissions from all roles
  const userPermissions = computed(() => {
    const user = getCurrentUser();
    if (!user?.roles) return [];
    return [
      ...new Set(
        user.roles.flatMap((role) => role.permissions?.map((p: Permission) => p.id) ?? []),
      ),
    ];
  });

  const userRoles = computed(() => {
    const user = getCurrentUser();
    if (user && user.roles) {
      return user.roles.map((role) => role.name);
    }
    return [];
  });

  // Actions
  function getCurrentUser(): User | undefined {
    const token = localStorage.getItem('access_token');
    if (!token) return undefined;
    const user = getUserFromToken(token);
    return user;
  }

  function getUserFromToken(token: string): User {
    try {
      const decodedToken = jwtDecode<JwtPayload>(token);
      const decrypted = utilsStore.decryptObject(secretKey, decodedToken.sub!);
      if (!decrypted) throw new Error('Failed to decrypt user object');

      const user = decrypted as unknown as User;

      // 🔒 เก็บ permission (จาก role.permissions) เป็น string เข้ารหัสใน localStorage
      if (user.roles?.[0]?.permissions) {
        const perms =
          utilsStore.encryptString(
            secretKey,
            JSON.stringify(user.roles?.[0]?.permissions),
            false,
          ) || '';
        localStorage.setItem('perms', perms);
      }

      return user;
    } catch (e) {
      console.error('Token decode or decrypt failed:', e);
      return {
        id: 0,
        roleId: 0,
        name: '',
        email: '',
        password: '',
        roles: [
          {
            id: 0,
            userId: 0,
            name: 'Guest', // ✅ ต้องเป็นค่าที่อยู่ใน RoleEnum
            permissions: [],
          },
        ],
      };
    }
  }

  function logout() {
    localStorage.removeItem('access_token');
    localStorage.removeItem('perms');
    localStorage.removeItem('hasVisited');
    localStorage.removeItem('currentRole');
    // window.location.reload();
    window.location.href = '/login';
  }

  function showNotifyDialog(message: string) {
    notifyMessage.value = message;
    notifyDialog.value = true;
  }

  async function loginBuu(): Promise<boolean> {
    utilsStore.isLoading = true;
    incorrectUsernamePasswordStatus.value = false;
    try {
      const encryptedUsername = utilsStore.encryptString(
        'loginBuu_username',
        loginUsername.value,
        false,
      );
      const encryptedPassword = utilsStore.encryptString(
        'loginBuu_password',
        loginPassword.value,
        false,
      );
      localStorage.removeItem('access_token');
      localStorage.removeItem('perms');
      const response = await authService.loginBuu(encryptedUsername!, encryptedPassword!);
      localStorage.setItem('access_token', response.data.access_token);

      return true;
    } catch (error) {
      const axiosError = error as AxiosError<{ message: string }>;
      if (axiosError.response?.data.message === 'ชื่อผู้ใช้หรือรหัสผ่านไม่ถูกต้อง') {
        incorrectUsernamePasswordStatus.value = true;
      } else {
        console.error('Login error:', axiosError);
        showNotifyDialog('เกิดข้อผิดพลาดในการเข้าสู่ระบบ');
      }
      return false;
    } finally {
      utilsStore.isLoading = false;
    }
  }

  // Return all permission ids from both roles[].permissions and psnPermissions
  function getUserListPermId(user?: User): number[] {
    if (!user) return [];
    // From roles[].permissions
    const rolePerms = user.roles?.flatMap((role) => role.permissions?.map((p) => p.id) ?? []) ?? [];
    // From psnPermissions
    const psnPerms = Array.isArray(user.psnPermissions)
      ? user.psnPermissions.map((p) => p.perId)
      : [];
    // Merge and dedupe
    return Array.from(new Set([...rolePerms, ...psnPerms]));
  }

  // Helper functions
  function hasPermission(permId: number): boolean {
    return userPermissions.value.includes(permId);
  }
  function hasAnyPermission(permIds: number[]): boolean {
    return permIds.some((id) => userPermissions.value.includes(id));
  }

  return {
    // State
    loginUsername,
    loginPassword,
    incorrectUsernamePasswordStatus,
    notifyDialog,
    notifyMessage,
    // Getters
    isLoggedIn,
    userPermissions,
    currentRoleName,
    userRoles,
    // Actions
    loginBuu,
    getCurrentUser,
    getUserPermIds: getUserListPermId,
    logout,
    showNotifyDialog,
    setCurrentRole,
    // Helper functions
    hasPermission,
    hasAnyPermission,
  };
});
